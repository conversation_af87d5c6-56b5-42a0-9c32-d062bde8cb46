import MockApiServer from './test-mock-api-server.js';
import { run } from './test-main-standalone.js';

console.log('🔍 REQUEST TRACKING TEST');
console.log('=' .repeat(60));
console.log('🎯 Monitoring all API calls to detect duplicate requests');
console.log('📊 Tracking both start and status endpoints');
console.log('');

class RequestTracker {
    constructor() {
        this.startRequests = [];
        this.statusRequests = [];
        this.mockServer = null;
    }

    async setupTracking() {
        console.log('🔧 Setting up request tracking...');
        
        // Create mock server with enhanced logging
        this.mockServer = new MockApiServer(3001);
        
        // Override the original routes to add tracking
        const originalApp = this.mockServer.app;
        
        // Track start requests
        originalApp.post('/webhook/realapifysendrequest', (req, res, next) => {
            const timestamp = new Date().toISOString();
            const requestData = {
                timestamp,
                body: req.body,
                headers: req.headers,
                ip: req.ip
            };
            
            this.startRequests.push(requestData);
            console.log(`📥 START REQUEST #${this.startRequests.length} at ${timestamp}`);
            console.log(`   Body: ${JSON.stringify(req.body)}`);
            console.log(`   User-Agent: ${req.headers['user-agent'] || 'Not provided'}`);
            
            // Continue with original handler
            next();
        });
        
        // Track status requests  
        originalApp.post('/webhook/realstatuscheckapify', (req, res, next) => {
            const timestamp = new Date().toISOString();
            const requestData = {
                timestamp,
                body: req.body,
                headers: req.headers,
                ip: req.ip
            };
            
            this.statusRequests.push(requestData);
            console.log(`📥 STATUS REQUEST #${this.statusRequests.length} at ${timestamp}`);
            console.log(`   Record ID: ${req.body.record_id}`);
            
            // Continue with original handler
            next();
        });
        
        await this.mockServer.start();
        console.log('✅ Request tracking enabled');
        
        // Wait for server to be ready
        await new Promise(resolve => setTimeout(resolve, 2000));
    }

    async testSingleExecution() {
        console.log('\n🧪 TEST: Single Execution Request Tracking');
        console.log('-' .repeat(50));
        
        // Clear previous tracking
        this.startRequests = [];
        this.statusRequests = [];
        
        // Configure immediate completion to minimize status calls
        const recordId = 'tracking-test-001';
        this.mockServer.enrichmentSessions.set(recordId, {
            currentStep: 0,
            statusSequence: [
                this.mockServer.generator.generateResponse('Completed') // Immediate completion
            ],
            startTime: Date.now(),
            scenario: 'immediate-completion'
        });

        console.log('🚀 Executing single enrichment request...');
        
        try {
            const testInput = {
                apolloLink: "https://app.apollo.io/#/tracking-test",
                noOfLeads: 1000,
                fileName: "tracking-test"
            };

            const result = await run(testInput);
            console.log(`✅ Execution completed with status: ${result.enrichment_status}`);
            
        } catch (error) {
            console.log(`❌ Execution failed: ${error.message}`);
        }

        // Analyze the requests
        this.analyzeRequests();
    }

    analyzeRequests() {
        console.log('\n📊 REQUEST ANALYSIS');
        console.log('=' .repeat(60));
        
        console.log(`📤 START REQUESTS (${this.startRequests.length} total):`);
        this.startRequests.forEach((req, index) => {
            console.log(`   ${index + 1}. ${req.timestamp}`);
            console.log(`      Apollo Link: ${req.body.apolloLink}`);
            console.log(`      File Name: ${req.body.fileName}`);
            console.log(`      Leads: ${req.body.noOfLeads}`);
        });
        
        console.log(`\n📥 STATUS REQUESTS (${this.statusRequests.length} total):`);
        this.statusRequests.forEach((req, index) => {
            console.log(`   ${index + 1}. ${req.timestamp}`);
            console.log(`      Record ID: ${req.body.record_id}`);
        });
        
        // Check for duplicates
        console.log('\n🔍 DUPLICATE ANALYSIS:');
        
        if (this.startRequests.length > 1) {
            console.log(`⚠️ WARNING: ${this.startRequests.length} start requests detected!`);
            console.log('❌ POTENTIAL ISSUE: Multiple calls to SEARCHLEADS_API_URL');
            
            // Check if they're identical
            const firstRequest = this.startRequests[0];
            const duplicates = this.startRequests.slice(1).filter(req => 
                JSON.stringify(req.body) === JSON.stringify(firstRequest.body)
            );
            
            if (duplicates.length > 0) {
                console.log(`🚨 DUPLICATE REQUESTS: ${duplicates.length + 1} identical start requests found!`);
            }
        } else {
            console.log('✅ START REQUESTS: Only 1 request (correct)');
        }
        
        console.log(`📊 STATUS REQUESTS: ${this.statusRequests.length} (expected for polling)`);
        
        // Summary
        console.log('\n🎯 SUMMARY:');
        if (this.startRequests.length === 1) {
            console.log('✅ NO DUPLICATE START REQUESTS - Implementation is correct');
            console.log('✅ SEARCHLEADS_API_URL called exactly once per execution');
        } else {
            console.log('❌ DUPLICATE START REQUESTS DETECTED');
            console.log('🔧 Investigation needed in the main logic');
        }
        
        console.log('=' .repeat(60));
    }

    async cleanup() {
        console.log('\n🧹 Cleaning up...');
        if (this.mockServer) {
            await this.mockServer.stop();
            console.log('✅ Mock server stopped');
        }
    }

    async runTest() {
        try {
            await this.setupTracking();
            await this.testSingleExecution();
        } catch (error) {
            console.error('💥 Request tracking test failed:', error.message);
        } finally {
            await this.cleanup();
        }
    }
}

// Run the tracking test
if (import.meta.url === `file://${process.argv[1]}`) {
    const tracker = new RequestTracker();
    tracker.runTest().catch(console.error);
}

export default RequestTracker;
