console.log('🧪 Quick Verification Test');
console.log('=' .repeat(50));

async function quickTest() {
    try {
        console.log('1. Testing imports...');
        
        // Test webhook generator
        const { TestWebhookGenerator } = await import('./test-webhook-generator.js');
        const generator = new TestWebhookGenerator();
        const response = generator.generateResponse('Completed');
        console.log('✅ Webhook generator working');
        console.log('📋 Sample response:', JSON.stringify(response[0], null, 2));
        
        console.log('\n2. Testing mock server import...');
        const MockApiServer = (await import('./test-mock-api-server.js')).default;
        console.log('✅ Mock server import successful');
        
        console.log('\n3. Testing mock server startup...');
        const server = new MockApiServer(3002); // Use different port
        await server.start();
        console.log('✅ Mock server started successfully');
        
        console.log('\n4. Testing server info...');
        const info = server.getInfo();
        console.log('📊 Server info:', info);
        
        console.log('\n5. Testing server shutdown...');
        await server.stop();
        console.log('✅ Mock server stopped successfully');
        
        console.log('\n🎉 All quick tests passed!');
        
    } catch (error) {
        console.error('❌ Quick test failed:', error.message);
        console.error('Stack:', error.stack);
    }
}

quickTest();
