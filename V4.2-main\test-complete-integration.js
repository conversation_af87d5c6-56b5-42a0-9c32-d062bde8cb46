import MockApiServer from './test-mock-api-server.js';
import { spawn } from 'child_process';

/**
 * Complete Integration Test with Real Test Webhooks
 * Tests the actual main.js against the configured test webhook endpoints
 */

console.log('🚀 Starting COMPLETE Integration Test with Real Test Webhooks');
console.log('=' .repeat(80));
console.log('📋 Using test webhook endpoints from .env file');
console.log('🎯 Testing actual main.js logic with real HTTP calls');
console.log('');

class CompleteIntegrationTester {
    constructor() {
        this.mockServer = new MockApiServer(3001);
        this.testResults = [];
    }

    /**
     * Start the mock server
     */
    async startMockServer() {
        console.log('🔧 Starting mock API server on port 3001...');
        await this.mockServer.start();
        console.log('✅ Mock server is ready to receive requests');
        
        // Wait for server to be fully ready
        await new Promise(resolve => setTimeout(resolve, 2000));
    }

    /**
     * Stop the mock server
     */
    async stopMockServer() {
        console.log('🛑 Stopping mock server...');
        await this.mockServer.stop();
        console.log('✅ Mock server stopped');
    }

    /**
     * Run the standalone main with test input
     */
    async runStandaloneMain(testInput, testName, timeout = 45000) {
        return new Promise((resolve, reject) => {
            console.log(`🚀 Running ${testName}...`);
            console.log(`📋 Input: ${JSON.stringify(testInput)}`);
            
            const child = spawn('node', ['test-main-standalone.js'], {
                stdio: ['pipe', 'pipe', 'pipe'],
                env: { ...process.env }
            });

            let stdout = '';
            let stderr = '';
            let resolved = false;

            // Send input to the process
            child.stdin.write(JSON.stringify(testInput));
            child.stdin.end();

            child.stdout.on('data', (data) => {
                const output = data.toString();
                stdout += output;
                console.log('📤', output.trim());
            });

            child.stderr.on('data', (data) => {
                const output = data.toString();
                stderr += output;
                console.log('📤 ERR:', output.trim());
            });

            child.on('close', (code) => {
                if (!resolved) {
                    resolved = true;
                    console.log(`🏁 Process finished with exit code: ${code}`);
                    resolve({
                        code,
                        stdout,
                        stderr,
                        success: code === 0
                    });
                }
            });

            child.on('error', (error) => {
                if (!resolved) {
                    resolved = true;
                    console.log(`💥 Process error: ${error.message}`);
                    reject(error);
                }
            });

            // Timeout handling
            setTimeout(() => {
                if (!resolved) {
                    resolved = true;
                    console.log(`⏰ Test timed out after ${timeout}ms`);
                    child.kill();
                    resolve({
                        code: -1,
                        stdout,
                        stderr,
                        success: false,
                        timedOut: true
                    });
                }
            }, timeout);
        });
    }

    /**
     * Test 1: Immediate completion scenario
     */
    async testImmediateCompletion() {
        console.log('\n🧪 TEST 1: Immediate Completion on First Status Check');
        console.log('=' .repeat(70));
        console.log('🎯 Goal: Verify polling stops IMMEDIATELY on Completed status');

        try {
            // Configure mock server for immediate completion
            this.mockServer.enrichmentSessions.clear();
            const recordId = 'test-immediate-001';
            
            // Set up immediate completion scenario
            this.mockServer.enrichmentSessions.set(recordId, {
                currentStep: 0,
                statusSequence: [
                    this.mockServer.generator.generateResponse('Completed') // Immediate completion
                ],
                startTime: Date.now(),
                scenario: 'immediate-completion'
            });

            const testInput = {
                apolloLink: "https://app.apollo.io/#/test-immediate",
                noOfLeads: 1000,
                fileName: "test-immediate-completion"
            };

            const result = await this.runStandaloneMain(testInput, 'Immediate Completion Test');
            
            // Analyze results
            const hasCompletionMessage = result.stdout.includes('Enrichment completed successfully');
            const hasImmediateStop = result.stdout.includes('Stopping polling immediately');
            const hasAttempt1 = result.stdout.includes('Attempt 1');
            const hasAttempt2 = result.stdout.includes('Attempt 2');
            
            const testPassed = result.success && 
                              hasCompletionMessage && 
                              hasImmediateStop && 
                              hasAttempt1 && 
                              !hasAttempt2; // Should NOT have attempt 2

            console.log(`\n📊 TEST 1 RESULTS:`);
            console.log(`   Exit Code: ${result.code}`);
            console.log(`   Success: ${result.success}`);
            console.log(`   Has Completion Message: ${hasCompletionMessage}`);
            console.log(`   Has Immediate Stop: ${hasImmediateStop}`);
            console.log(`   Has Attempt 1: ${hasAttempt1}`);
            console.log(`   Has Attempt 2: ${hasAttempt2} (should be false)`);
            console.log(`   🎯 RESULT: ${testPassed ? '✅ PASSED' : '❌ FAILED'}`);

            this.testResults.push({
                testName: 'Immediate Completion',
                passed: testPassed,
                details: {
                    exitCode: result.code,
                    hasCompletionMessage,
                    hasImmediateStop,
                    hasAttempt1,
                    hasAttempt2,
                    timedOut: result.timedOut
                }
            });

            return testPassed;

        } catch (error) {
            console.log(`❌ TEST 1 FAILED: ${error.message}`);
            this.testResults.push({
                testName: 'Immediate Completion',
                passed: false,
                error: error.message
            });
            return false;
        }
    }

    /**
     * Test 2: Multiple polling attempts before completion
     */
    async testDelayedCompletion() {
        console.log('\n🧪 TEST 2: Delayed Completion After Multiple Polls');
        console.log('=' .repeat(70));
        console.log('🎯 Goal: Verify continued polling until Completed status');

        try {
            // Clear previous sessions
            this.mockServer.enrichmentSessions.clear();
            const recordId = 'test-delayed-001';
            
            // Set up delayed completion scenario
            this.mockServer.enrichmentSessions.set(recordId, {
                currentStep: 0,
                statusSequence: [
                    this.mockServer.generator.generateResponse('InProgress'),
                    this.mockServer.generator.generateResponse('InProgress'),
                    this.mockServer.generator.generateResponse('inqueue'),
                    this.mockServer.generator.generateResponse('Completed')
                ],
                startTime: Date.now(),
                scenario: 'delayed-completion'
            });

            const testInput = {
                apolloLink: "https://app.apollo.io/#/test-delayed",
                noOfLeads: 1000,
                fileName: "test-delayed-completion"
            };

            const result = await this.runStandaloneMain(testInput, 'Delayed Completion Test');
            
            // Analyze results
            const hasCompletionMessage = result.stdout.includes('Enrichment completed successfully');
            const hasMultipleAttempts = result.stdout.includes('Attempt 3') || result.stdout.includes('Attempt 4');
            const hasInProgressStatus = result.stdout.includes('Status: InProgress');
            const hasInQueueStatus = result.stdout.includes('Status: inqueue');
            
            const testPassed = result.success && 
                              hasCompletionMessage && 
                              hasMultipleAttempts &&
                              (hasInProgressStatus || hasInQueueStatus);

            console.log(`\n📊 TEST 2 RESULTS:`);
            console.log(`   Exit Code: ${result.code}`);
            console.log(`   Success: ${result.success}`);
            console.log(`   Has Completion Message: ${hasCompletionMessage}`);
            console.log(`   Has Multiple Attempts: ${hasMultipleAttempts}`);
            console.log(`   Has InProgress Status: ${hasInProgressStatus}`);
            console.log(`   Has InQueue Status: ${hasInQueueStatus}`);
            console.log(`   🎯 RESULT: ${testPassed ? '✅ PASSED' : '❌ FAILED'}`);

            this.testResults.push({
                testName: 'Delayed Completion',
                passed: testPassed,
                details: {
                    exitCode: result.code,
                    hasCompletionMessage,
                    hasMultipleAttempts,
                    hasInProgressStatus,
                    hasInQueueStatus,
                    timedOut: result.timedOut
                }
            });

            return testPassed;

        } catch (error) {
            console.log(`❌ TEST 2 FAILED: ${error.message}`);
            this.testResults.push({
                testName: 'Delayed Completion',
                passed: false,
                error: error.message
            });
            return false;
        }
    }

    /**
     * Generate final test report
     */
    generateFinalReport() {
        console.log('\n📊 COMPLETE INTEGRATION TEST REPORT');
        console.log('=' .repeat(80));

        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(test => test.passed).length;
        const failedTests = totalTests - passedTests;

        console.log(`📈 Total Tests: ${totalTests}`);
        console.log(`✅ Passed: ${passedTests}`);
        console.log(`❌ Failed: ${failedTests}`);
        console.log(`📊 Success Rate: ${totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0}%`);
        console.log('');

        // Detailed results
        console.log('📋 DETAILED RESULTS:');
        this.testResults.forEach((test, index) => {
            console.log(`${index + 1}. ${test.testName}: ${test.passed ? '✅ PASSED' : '❌ FAILED'}`);
            if (test.error) {
                console.log(`   Error: ${test.error}`);
            }
            if (test.details) {
                console.log(`   Exit Code: ${test.details.exitCode}`);
                if (test.details.timedOut) {
                    console.log(`   ⚠️ Test timed out`);
                }
            }
        });

        console.log('');
        
        // Final verdict
        if (passedTests === totalTests && totalTests > 0) {
            console.log('🎉 ALL INTEGRATION TESTS PASSED!');
            console.log('✅ The refactored implementation works correctly with real test webhooks');
            console.log('✅ Immediate termination on Completed status VERIFIED');
            console.log('✅ Continued polling for InProgress/inqueue statuses VERIFIED');
            console.log('✅ Real HTTP calls to test endpoints working correctly');
            console.log('');
            console.log('🏆 TESTING COMPLETE - IMPLEMENTATION IS READY FOR PRODUCTION');
        } else {
            console.log('⚠️ SOME TESTS FAILED');
            console.log('❌ Please review the implementation or test setup');
            console.log('🔧 Check the detailed results above for specific issues');
        }

        console.log('=' .repeat(80));
        
        return passedTests === totalTests;
    }

    /**
     * Run all integration tests
     */
    async runAllTests() {
        try {
            // Start mock server
            await this.startMockServer();

            // Run tests
            await this.testImmediateCompletion();
            await this.testDelayedCompletion();

            // Generate final report
            const allPassed = this.generateFinalReport();
            
            return allPassed;

        } catch (error) {
            console.error('💥 Integration test suite failed:', error.message);
            return false;
        } finally {
            // Always cleanup
            await this.stopMockServer();
        }
    }
}

// Run the complete integration test
if (import.meta.url === `file://${process.argv[1]}`) {
    const tester = new CompleteIntegrationTester();
    tester.runAllTests()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('💥 Test execution failed:', error);
            process.exit(1);
        });
}

export default CompleteIntegrationTester;
