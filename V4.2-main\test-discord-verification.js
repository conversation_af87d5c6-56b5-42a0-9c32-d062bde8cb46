import DiscordWebhookNotifier from './test-discord-webhook.js';

console.log('🧪 Discord Webhook Verification Test');
console.log('=' .repeat(50));
console.log('📡 Testing Discord notifications for Failed and Cancelled scenarios');
console.log('🎯 You should receive 3 messages in your Discord channel');
console.log('');

async function testDiscordNotifications() {
    const discordNotifier = new DiscordWebhookNotifier();
    
    try {
        // Test 1: Connection test
        console.log('📤 Test 1: Sending connection test message...');
        const connectionResult = await discordNotifier.testConnection();
        console.log(`✅ Connection test: ${connectionResult.success ? 'SUCCESS' : 'FAILED'}`);
        if (!connectionResult.success) {
            console.log(`❌ Error: ${connectionResult.error}`);
        }
        
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
        
        // Test 2: Failed notification
        console.log('\n📤 Test 2: Sending Failed enrichment notification...');
        const failedResult = await discordNotifier.sendFailedNotification(
            {
                apolloLink: "https://app.apollo.io/#/test-failed",
                noOfLeads: 1000,
                fileName: "test-failed-notification"
            },
            {
                record_id: "test-failed-001",
                file_name: "test-failed-notification",
                requested_leads_count: "1000",
                enrichment_status: "Failed",
                error_message: "API rate limit exceeded - this is a test",
                failure_time: new Date().toISOString(),
                retry_possible: true,
                apollo_link: "https://app.apollo.io/#/test-failed"
            }
        );
        console.log(`✅ Failed notification: ${failedResult.success ? 'SUCCESS' : 'FAILED'}`);
        if (!failedResult.success) {
            console.log(`❌ Error: ${failedResult.error}`);
        }
        
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
        
        // Test 3: Cancelled notification
        console.log('\n📤 Test 3: Sending Cancelled enrichment notification...');
        const cancelledResult = await discordNotifier.sendCancelledNotification(
            {
                apolloLink: "https://app.apollo.io/#/test-cancelled",
                noOfLeads: 1000,
                fileName: "test-cancelled-notification"
            },
            {
                record_id: "test-cancelled-001",
                file_name: "test-cancelled-notification",
                requested_leads_count: "1000",
                enrichment_status: "Cancelled",
                cancellation_reason: "User requested cancellation - this is a test",
                cancelled_time: new Date().toISOString(),
                partial_results_available: false,
                apollo_link: "https://app.apollo.io/#/test-cancelled"
            }
        );
        console.log(`✅ Cancelled notification: ${cancelledResult.success ? 'SUCCESS' : 'FAILED'}`);
        if (!cancelledResult.success) {
            console.log(`❌ Error: ${cancelledResult.error}`);
        }
        
        // Summary
        console.log('\n📊 DISCORD VERIFICATION SUMMARY');
        console.log('=' .repeat(50));
        console.log(`📡 Webhook URL: ${discordNotifier.webhookUrl}`);
        console.log(`✅ Connection Test: ${connectionResult.success ? 'SENT' : 'FAILED'}`);
        console.log(`❌ Failed Notification: ${failedResult.success ? 'SENT' : 'FAILED'}`);
        console.log(`🛑 Cancelled Notification: ${cancelledResult.success ? 'SENT' : 'FAILED'}`);
        console.log('');
        
        const allSuccess = connectionResult.success && failedResult.success && cancelledResult.success;
        
        if (allSuccess) {
            console.log('🎉 ALL DISCORD NOTIFICATIONS SENT SUCCESSFULLY!');
            console.log('📱 Check your Discord channel - you should see 3 new messages:');
            console.log('   1. 🧪 Connection test message (green)');
            console.log('   2. 🚨 Failed enrichment notification (red)');
            console.log('   3. ⏹️ Cancelled enrichment notification (orange)');
            console.log('');
            console.log('💡 If you don\'t see them, check:');
            console.log('   - The correct Discord channel/server');
            console.log('   - Webhook permissions');
            console.log('   - Discord notification settings');
        } else {
            console.log('⚠️ Some Discord notifications failed to send');
            console.log('🔧 Check the webhook URL and Discord server settings');
        }
        
        console.log('=' .repeat(50));
        
    } catch (error) {
        console.error('💥 Discord verification test failed:', error.message);
    }
}

testDiscordNotifications();
