import MockApiServer from './test-mock-api-server.js';

/**
 * Apify Environment Simulation Test
 * Tests for double response errors and multiple executions
 */

console.log('🔍 APIFY ENVIRONMENT SIMULATION TEST');
console.log('=' .repeat(70));
console.log('🎯 Checking for double response errors and multiple executions');
console.log('📋 Simulating Apify platform behavior');
console.log('');

// Mock Apify Actor for testing
const MockActor = {
    initCalled: 0,
    getInputCalled: 0,
    setValueCalled: 0,
    exitCalled: 0,
    chargeCalled: 0,
    
    async init() {
        this.initCalled++;
        console.log(`🔧 Mock Actor.init() called (${this.initCalled} times)`);
    },
    
    async getInput() {
        this.getInputCalled++;
        console.log(`📥 Mock Actor.getInput() called (${this.getInputCalled} times)`);
        return {
            apolloLink: "https://app.apollo.io/#/apify-test",
            noOfLeads: 1000,
            fileName: "apify-simulation-test"
        };
    },
    
    async setValue(key, value) {
        this.setValueCalled++;
        console.log(`💾 Mock Actor.setValue('${key}') called (${this.setValueCalled} times)`);
    },
    
    async exit() {
        this.exitCalled++;
        console.log(`🚪 Mock Actor.exit() called (${this.exitCalled} times)`);
    },
    
    async charge(options) {
        this.chargeCalled++;
        console.log(`💳 Mock Actor.charge() called (${this.chargeCalled} times)`);
        return { success: true };
    },
    
    reset() {
        this.initCalled = 0;
        this.getInputCalled = 0;
        this.setValueCalled = 0;
        this.exitCalled = 0;
        this.chargeCalled = 0;
    },
    
    getStats() {
        return {
            initCalled: this.initCalled,
            getInputCalled: this.getInputCalled,
            setValueCalled: this.setValueCalled,
            exitCalled: this.exitCalled,
            chargeCalled: this.chargeCalled
        };
    }
};

class ApifySimulationTester {
    constructor() {
        this.mockServer = new MockApiServer(3001);
        this.originalActor = null;
    }

    async setupApifyEnvironment() {
        console.log('🔧 Setting up Apify environment simulation...');
        
        // Start mock server
        await this.mockServer.start();
        console.log('✅ Mock SearchLeads API server started');
        
        // Replace global Actor with our mock
        global.Actor = MockActor;
        console.log('✅ Mock Apify Actor installed');
        
        // Wait for server to be ready
        await new Promise(resolve => setTimeout(resolve, 2000));
    }

    async testSingleExecution() {
        console.log('\n🧪 TEST 1: Single Execution Simulation');
        console.log('-' .repeat(50));
        
        // Reset tracking
        MockActor.reset();
        this.mockServer.resetRequestTracking();
        
        // Configure immediate completion
        const recordId = 'apify-test-001';
        this.mockServer.enrichmentSessions.set(recordId, {
            currentStep: 0,
            statusSequence: [
                this.mockServer.generator.generateResponse('Completed')
            ],
            startTime: Date.now(),
            scenario: 'immediate-completion'
        });

        console.log('🚀 Simulating Apify actor execution...');
        
        try {
            // Import and execute the main logic
            // We need to dynamically import to avoid module caching issues
            const mainModule = await import('./main.js?' + Date.now());
            
            // Wait for execution to complete
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            console.log('✅ Execution completed');
            
        } catch (error) {
            console.log(`❌ Execution failed: ${error.message}`);
        }

        // Analyze results
        this.analyzeApifyExecution();
    }

    async testMultipleExecutions() {
        console.log('\n🧪 TEST 2: Multiple Execution Detection');
        console.log('-' .repeat(50));
        
        // Reset tracking
        MockActor.reset();
        this.mockServer.resetRequestTracking();
        
        console.log('🚀 Simulating potential double execution...');
        
        try {
            // Simulate what might happen if main.js gets executed multiple times
            const promises = [];
            
            for (let i = 0; i < 2; i++) {
                console.log(`📤 Starting execution ${i + 1}...`);
                
                // Configure different sessions for each execution
                const recordId = `apify-multi-test-${i + 1}`;
                this.mockServer.enrichmentSessions.set(recordId, {
                    currentStep: 0,
                    statusSequence: [
                        this.mockServer.generator.generateResponse('Completed')
                    ],
                    startTime: Date.now(),
                    scenario: 'immediate-completion'
                });
                
                // Import with different query to avoid caching
                const mainModule = await import('./main.js?' + Date.now() + '-' + i);
                promises.push(new Promise(resolve => setTimeout(resolve, 3000)));
            }
            
            await Promise.all(promises);
            console.log('✅ Multiple execution test completed');
            
        } catch (error) {
            console.log(`❌ Multiple execution test failed: ${error.message}`);
        }

        // Analyze results
        this.analyzeMultipleExecution();
    }

    analyzeApifyExecution() {
        const actorStats = MockActor.getStats();
        const requestStats = this.mockServer.getRequestStats();
        
        console.log('\n📊 APIFY EXECUTION ANALYSIS');
        console.log('=' .repeat(50));
        
        console.log('🎭 ACTOR METHOD CALLS:');
        console.log(`   init(): ${actorStats.initCalled}`);
        console.log(`   getInput(): ${actorStats.getInputCalled}`);
        console.log(`   setValue(): ${actorStats.setValueCalled}`);
        console.log(`   exit(): ${actorStats.exitCalled}`);
        console.log(`   charge(): ${actorStats.chargeCalled}`);
        
        console.log('\n📡 API REQUEST CALLS:');
        console.log(`   START requests: ${requestStats.startRequests}`);
        console.log(`   STATUS requests: ${requestStats.statusRequests}`);
        
        // Check for issues
        console.log('\n🔍 ISSUE DETECTION:');
        
        if (requestStats.startRequests > 1) {
            console.log('🚨 DOUBLE RESPONSE ERROR DETECTED!');
            console.log(`❌ ${requestStats.startRequests} start requests made`);
            console.log('🔧 This could cause double response errors on Apify');
        } else {
            console.log('✅ No double response errors detected');
        }
        
        if (actorStats.getInputCalled > 1) {
            console.log('⚠️ Multiple getInput() calls detected');
            console.log('🔧 This might indicate multiple executions');
        }
        
        if (actorStats.exitCalled > 1) {
            console.log('⚠️ Multiple exit() calls detected');
            console.log('🔧 This could cause Apify platform issues');
        }
    }

    analyzeMultipleExecution() {
        const actorStats = MockActor.getStats();
        const requestStats = this.mockServer.getRequestStats();
        
        console.log('\n📊 MULTIPLE EXECUTION ANALYSIS');
        console.log('=' .repeat(50));
        
        console.log('🎭 TOTAL ACTOR METHOD CALLS:');
        console.log(`   init(): ${actorStats.initCalled}`);
        console.log(`   getInput(): ${actorStats.getInputCalled}`);
        console.log(`   setValue(): ${actorStats.setValueCalled}`);
        console.log(`   exit(): ${actorStats.exitCalled}`);
        
        console.log('\n📡 TOTAL API REQUEST CALLS:');
        console.log(`   START requests: ${requestStats.startRequests}`);
        console.log(`   STATUS requests: ${requestStats.statusRequests}`);
        
        console.log('\n🚨 DOUBLE EXECUTION DETECTION:');
        if (requestStats.startRequests > 1) {
            console.log(`❌ CONFIRMED: ${requestStats.startRequests} start requests detected`);
            console.log('🔧 This WILL cause double response errors on Apify platform');
            console.log('💡 Root cause: Multiple executions of main.js');
        } else {
            console.log('✅ No multiple executions detected');
        }
    }

    async cleanup() {
        console.log('\n🧹 Cleaning up simulation environment...');
        await this.mockServer.stop();
        console.log('✅ Cleanup complete');
    }

    async runSimulation() {
        try {
            await this.setupApifyEnvironment();
            await this.testSingleExecution();
            await this.testMultipleExecutions();
            
            console.log('\n🎯 APIFY SIMULATION SUMMARY');
            console.log('=' .repeat(70));
            console.log('📋 Check the analysis above for double response error detection');
            console.log('🔧 If issues found, the main.js needs modification for Apify platform');
            
        } catch (error) {
            console.error('💥 Apify simulation failed:', error.message);
        } finally {
            await this.cleanup();
        }
    }
}

// Run the simulation
if (import.meta.url === `file://${process.argv[1]}`) {
    const tester = new ApifySimulationTester();
    tester.runSimulation().catch(console.error);
}

export default ApifySimulationTester;
