import MockApiServer from './test-mock-api-server.js';
import axios from 'axios';

console.log('🔍 SIMPLE REQUEST DUPLICATION CHECK');
console.log('=' .repeat(60));
console.log('🎯 Testing if SEARCHLEADS_API_URL gets called multiple times');
console.log('');

async function checkRequestDuplication() {
    let mockServer = null;
    let startRequestCount = 0;
    let statusRequestCount = 0;
    
    try {
        // Start mock server
        console.log('🔧 Starting mock server with request counting...');
        mockServer = new MockApiServer(3001);
        
        // Override the start endpoint to count requests
        const originalStartHandler = mockServer.app._router.stack.find(
            layer => layer.route && layer.route.path === '/webhook/realapifysendrequest'
        );
        
        // Add request counting middleware
        mockServer.app.use('/webhook/realapifysendrequest', (req, res, next) => {
            startRequestCount++;
            console.log(`📥 START REQUEST #${startRequestCount} received`);
            console.log(`   Body: ${JSON.stringify(req.body)}`);
            next();
        });
        
        mockServer.app.use('/webhook/realstatuscheckapify', (req, res, next) => {
            statusRequestCount++;
            console.log(`📥 STATUS REQUEST #${statusRequestCount} received`);
            console.log(`   Record ID: ${req.body.record_id}`);
            next();
        });
        
        await mockServer.start();
        console.log('✅ Mock server started with request tracking');
        
        // Wait for server to be ready
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Test 1: Manual API calls to verify tracking
        console.log('\n🧪 TEST 1: Manual API call verification');
        console.log('-' .repeat(40));
        
        console.log('📤 Sending manual start request...');
        const startResponse = await axios.post('http://localhost:3001/webhook/realapifysendrequest', {
            apolloLink: "https://app.apollo.io/#/manual-test",
            noOfLeads: 1000,
            fileName: "manual-test"
        });
        
        console.log('📤 Sending manual status request...');
        await axios.post('http://localhost:3001/webhook/realstatuscheckapify', {
            record_id: startResponse.data.record_id
        });
        
        console.log(`📊 After manual calls: START=${startRequestCount}, STATUS=${statusRequestCount}`);
        
        // Reset counters for main test
        const manualStartCount = startRequestCount;
        const manualStatusCount = statusRequestCount;
        
        // Test 2: Check the actual main logic
        console.log('\n🧪 TEST 2: Testing main logic for duplicates');
        console.log('-' .repeat(40));
        
        // Configure immediate completion
        const recordId = 'duplicate-check-001';
        mockServer.enrichmentSessions.set(recordId, {
            currentStep: 0,
            statusSequence: [
                mockServer.generator.generateResponse('Completed')
            ],
            startTime: Date.now(),
            scenario: 'immediate-completion'
        });
        
        console.log('🚀 Testing with test-main-standalone.js...');
        
        // Import and run the standalone main
        const { run } = await import('./test-main-standalone.js');
        
        const testInput = {
            apolloLink: "https://app.apollo.io/#/duplicate-test",
            noOfLeads: 1000,
            fileName: "duplicate-test"
        };
        
        try {
            const result = await run(testInput);
            console.log(`✅ Main execution completed: ${result.enrichment_status}`);
        } catch (error) {
            console.log(`❌ Main execution failed: ${error.message}`);
        }
        
        // Calculate requests from main logic only
        const mainStartRequests = startRequestCount - manualStartCount;
        const mainStatusRequests = statusRequestCount - manualStatusCount;
        
        console.log('\n📊 FINAL REQUEST ANALYSIS');
        console.log('=' .repeat(60));
        console.log(`📤 Total START requests: ${startRequestCount}`);
        console.log(`   Manual test requests: ${manualStartCount}`);
        console.log(`   Main logic requests: ${mainStartRequests}`);
        console.log('');
        console.log(`📥 Total STATUS requests: ${statusRequestCount}`);
        console.log(`   Manual test requests: ${manualStatusCount}`);
        console.log(`   Main logic requests: ${mainStatusRequests}`);
        console.log('');
        
        // Analysis
        if (mainStartRequests === 1) {
            console.log('✅ RESULT: NO DUPLICATION DETECTED');
            console.log('✅ SEARCHLEADS_API_URL called exactly ONCE per execution');
            console.log('✅ Implementation is working correctly');
        } else if (mainStartRequests > 1) {
            console.log('🚨 WARNING: DUPLICATE START REQUESTS DETECTED!');
            console.log(`❌ SEARCHLEADS_API_URL called ${mainStartRequests} times`);
            console.log('🔧 This indicates a bug in the main logic');
        } else {
            console.log('⚠️ No start requests detected from main logic');
            console.log('🔧 This might indicate a test setup issue');
        }
        
        console.log('=' .repeat(60));
        
    } catch (error) {
        console.error('💥 Request duplication check failed:', error.message);
    } finally {
        if (mockServer) {
            console.log('\n🧹 Stopping mock server...');
            await mockServer.stop();
            console.log('✅ Cleanup complete');
        }
    }
}

checkRequestDuplication();
