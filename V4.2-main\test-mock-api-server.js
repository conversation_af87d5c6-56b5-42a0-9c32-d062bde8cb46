import express from 'express';
import TestWebhookGenerator from './test-webhook-generator.js';

/**
 * Mock API Server for SearchLeads Integration Testing
 * Simulates real SearchLeads API endpoints with controllable responses
 */

class MockApiServer {
    constructor(port = 3001) {
        this.app = express();
        this.port = port;
        this.generator = new TestWebhookGenerator();
        this.server = null;
        
        // Store active enrichment sessions
        this.enrichmentSessions = new Map();
        
        this.setupMiddleware();
        this.setupRoutes();
    }

    setupMiddleware() {
        this.app.use(express.json());
        this.app.use((req, res, next) => {
            console.log(`📡 Mock API: ${req.method} ${req.path}`, req.body);
            next();
        });
    }

    setupRoutes() {
        // Mock the enrichment start endpoint
        this.app.post('/webhook/realapifysendrequest', (req, res) => {
            console.log('🚀 Mock API: Starting enrichment request');
            
            const recordId = this.generator.baseResponse.record_id;
            
            // Create a new enrichment session with predefined status sequence
            const statusSequence = this.createStatusSequence();
            this.enrichmentSessions.set(recordId, {
                currentStep: 0,
                statusSequence: statusSequence,
                startTime: Date.now(),
                requestData: req.body
            });
            
            console.log(`✅ Mock API: Created session ${recordId} with ${statusSequence.length} status steps`);
            
            res.json({
                record_id: recordId,
                status: 'Request received',
                message: 'Enrichment process started'
            });
        });

        // Mock the status check endpoint
        this.app.post('/webhook/realstatuscheckapify', (req, res) => {
            const { record_id } = req.body;
            console.log(`🔍 Mock API: Status check for record ${record_id}`);
            
            const session = this.enrichmentSessions.get(record_id);
            if (!session) {
                console.log('❌ Mock API: Session not found');
                return res.status(404).json({ error: 'Record not found' });
            }
            
            // Get current status from sequence
            const currentStatus = session.statusSequence[session.currentStep];
            console.log(`📊 Mock API: Returning status step ${session.currentStep + 1}/${session.statusSequence.length}: ${currentStatus[0].enrichment_status}`);
            
            // Move to next step for next request (unless it's the final step)
            if (session.currentStep < session.statusSequence.length - 1) {
                session.currentStep++;
            }
            
            res.json(currentStatus);
        });

        // Health check endpoint
        this.app.get('/health', (req, res) => {
            res.json({ 
                status: 'healthy', 
                activeSessions: this.enrichmentSessions.size,
                uptime: process.uptime()
            });
        });

        // Reset endpoint for testing
        this.app.post('/test/reset', (req, res) => {
            console.log('🔄 Mock API: Resetting all sessions');
            this.enrichmentSessions.clear();
            res.json({ message: 'All sessions cleared' });
        });

        // Configure specific test scenario
        this.app.post('/test/configure/:recordId', (req, res) => {
            const { recordId } = req.params;
            const { scenario } = req.body;
            
            console.log(`⚙️ Mock API: Configuring scenario '${scenario}' for record ${recordId}`);
            
            let statusSequence;
            switch (scenario) {
                case 'immediate-completion':
                    statusSequence = [
                        this.generator.generateResponse('Completed')
                    ];
                    break;
                case 'delayed-completion':
                    statusSequence = [
                        this.generator.generateResponse('InProgress'),
                        this.generator.generateResponse('InProgress'),
                        this.generator.generateResponse('Completed')
                    ];
                    break;
                case 'queue-then-complete':
                    statusSequence = [
                        this.generator.generateResponse('inqueue'),
                        this.generator.generateResponse('InProgress'),
                        this.generator.generateResponse('Completed')
                    ];
                    break;
                case 'failure':
                    statusSequence = [
                        this.generator.generateResponse('InProgress'),
                        this.generator.generateResponse('Failed')
                    ];
                    break;
                case 'cancellation':
                    statusSequence = [
                        this.generator.generateResponse('InProgress'),
                        this.generator.generateResponse('Cancelled')
                    ];
                    break;
                default:
                    return res.status(400).json({ error: 'Unknown scenario' });
            }
            
            this.enrichmentSessions.set(recordId, {
                currentStep: 0,
                statusSequence: statusSequence,
                startTime: Date.now(),
                scenario: scenario
            });
            
            res.json({ 
                message: `Scenario '${scenario}' configured for record ${recordId}`,
                steps: statusSequence.length
            });
        });
    }

    /**
     * Create a default status sequence for testing
     */
    createStatusSequence() {
        // Default: InProgress -> InProgress -> Completed (tests immediate termination)
        return [
            this.generator.generateResponse('InProgress'),
            this.generator.generateResponse('InProgress'),
            this.generator.generateResponse('Completed')
        ];
    }

    /**
     * Start the mock server
     */
    async start() {
        return new Promise((resolve, reject) => {
            this.server = this.app.listen(this.port, (err) => {
                if (err) {
                    reject(err);
                } else {
                    console.log(`🚀 Mock API Server started on port ${this.port}`);
                    console.log(`📡 Endpoints available:`);
                    console.log(`   POST http://localhost:${this.port}/webhook/realapifysendrequest`);
                    console.log(`   POST http://localhost:${this.port}/webhook/realstatuscheckapify`);
                    console.log(`   GET  http://localhost:${this.port}/health`);
                    console.log(`   POST http://localhost:${this.port}/test/reset`);
                    console.log(`   POST http://localhost:${this.port}/test/configure/:recordId`);
                    resolve();
                }
            });
        });
    }

    /**
     * Stop the mock server
     */
    async stop() {
        return new Promise((resolve) => {
            if (this.server) {
                this.server.close(() => {
                    console.log('🛑 Mock API Server stopped');
                    resolve();
                });
            } else {
                resolve();
            }
        });
    }

    /**
     * Get server info
     */
    getInfo() {
        return {
            port: this.port,
            baseUrl: `http://localhost:${this.port}`,
            activeSessions: this.enrichmentSessions.size,
            isRunning: this.server !== null
        };
    }
}

// Export for use in tests
export default MockApiServer;

// Run server if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    const server = new MockApiServer();
    
    // Handle graceful shutdown
    process.on('SIGINT', async () => {
        console.log('\n🛑 Shutting down mock server...');
        await server.stop();
        process.exit(0);
    });
    
    // Start the server
    server.start().catch(console.error);
}
