import MockApiServer from './test-mock-api-server.js';
import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';

/**
 * Real Integration Testing
 * Tests the actual main.js against a mock server with real HTTP calls
 */

class RealIntegrationTester {
    constructor() {
        this.mockServer = new MockApiServer(3001);
        this.testResults = [];
    }

    /**
     * Setup test environment
     */
    async setup() {
        console.log('🔧 Setting up real integration test environment...');
        
        // Start mock server
        await this.mockServer.start();
        
        // Backup original .env and use test .env
        if (fs.existsSync('.env')) {
            fs.copyFileSync('.env', '.env.backup');
            console.log('📋 Backed up original .env file');
        }
        
        fs.copyFileSync('.env.test', '.env');
        console.log('✅ Using test environment configuration');
        
        // Wait a moment for server to be ready
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    /**
     * Cleanup test environment
     */
    async cleanup() {
        console.log('🧹 Cleaning up test environment...');
        
        // Stop mock server
        await this.mockServer.stop();
        
        // Restore original .env
        if (fs.existsSync('.env.backup')) {
            fs.copyFileSync('.env.backup', '.env');
            fs.unlinkSync('.env.backup');
            console.log('📋 Restored original .env file');
        }
    }

    /**
     * Run test-main-standalone.js with test input and capture output
     */
    async runMainWithInput(testInput, timeout = 30000) {
        return new Promise((resolve, reject) => {
            console.log(`🚀 Running test-main-standalone.js with test input...`);

            const child = spawn('node', ['test-main-standalone.js'], {
                stdio: ['pipe', 'pipe', 'pipe'],
                env: { ...process.env, NODE_ENV: 'test' }
            });

            let stdout = '';
            let stderr = '';
            let resolved = false;

            // Send input to the process
            child.stdin.write(JSON.stringify(testInput));
            child.stdin.end();

            child.stdout.on('data', (data) => {
                const output = data.toString();
                stdout += output;
                console.log('📤 STDOUT:', output.trim());
            });

            child.stderr.on('data', (data) => {
                const output = data.toString();
                stderr += output;
                console.log('📤 STDERR:', output.trim());
            });

            child.on('close', (code) => {
                if (!resolved) {
                    resolved = true;
                    resolve({
                        code,
                        stdout,
                        stderr,
                        success: code === 0
                    });
                }
            });

            child.on('error', (error) => {
                if (!resolved) {
                    resolved = true;
                    reject(error);
                }
            });

            // Timeout handling
            setTimeout(() => {
                if (!resolved) {
                    resolved = true;
                    child.kill();
                    reject(new Error(`Test timed out after ${timeout}ms`));
                }
            }, timeout);
        });
    }

    /**
     * Test immediate termination on Completed status
     */
    async testImmediateCompletion() {
        console.log('\n🧪 REAL INTEGRATION TEST 1: Immediate Completion');
        console.log('=' .repeat(70));

        try {
            // Configure mock server for immediate completion
            const testRecordId = 'test-immediate-completion';
            await this.configureScenario(testRecordId, 'immediate-completion');

            const testInput = {
                apolloLink: "https://app.apollo.io/#/test",
                noOfLeads: 1000,
                fileName: "test-immediate"
            };

            const result = await this.runMainWithInput(testInput, 15000);
            
            // Check if the process completed successfully
            const completedSuccessfully = result.success && 
                                        result.stdout.includes('Enrichment completed successfully') &&
                                        result.stdout.includes('Stopping polling immediately');

            console.log(`📊 Test Result: ${completedSuccessfully ? '✅ PASSED' : '❌ FAILED'}`);
            console.log(`📋 Exit Code: ${result.code}`);
            console.log(`📈 Success: ${result.success}`);

            this.testResults.push({
                testName: 'Immediate Completion - Real Integration',
                passed: completedSuccessfully,
                details: {
                    exitCode: result.code,
                    success: result.success,
                    hasCompletionMessage: result.stdout.includes('Enrichment completed successfully'),
                    hasImmediateStop: result.stdout.includes('Stopping polling immediately')
                }
            });

            return completedSuccessfully;

        } catch (error) {
            console.log(`❌ Test failed with error: ${error.message}`);
            this.testResults.push({
                testName: 'Immediate Completion - Real Integration',
                passed: false,
                error: error.message
            });
            return false;
        }
    }

    /**
     * Test delayed completion (multiple polling attempts)
     */
    async testDelayedCompletion() {
        console.log('\n🧪 REAL INTEGRATION TEST 2: Delayed Completion');
        console.log('=' .repeat(70));

        try {
            // Reset server state
            await this.resetServer();

            const testRecordId = 'test-delayed-completion';
            await this.configureScenario(testRecordId, 'delayed-completion');

            const testInput = {
                apolloLink: "https://app.apollo.io/#/test",
                noOfLeads: 1000,
                fileName: "test-delayed"
            };

            const result = await this.runMainWithInput(testInput, 20000);
            
            // Check if polling happened multiple times before completion
            const multiplePolls = result.stdout.includes('Attempt 1') && 
                                result.stdout.includes('Attempt 2') &&
                                result.stdout.includes('Enrichment completed successfully');

            console.log(`📊 Test Result: ${multiplePolls ? '✅ PASSED' : '❌ FAILED'}`);
            console.log(`📋 Exit Code: ${result.code}`);

            this.testResults.push({
                testName: 'Delayed Completion - Multiple Polls',
                passed: multiplePolls,
                details: {
                    exitCode: result.code,
                    hasMultipleAttempts: result.stdout.includes('Attempt 2'),
                    completedSuccessfully: result.stdout.includes('Enrichment completed successfully')
                }
            });

            return multiplePolls;

        } catch (error) {
            console.log(`❌ Test failed with error: ${error.message}`);
            this.testResults.push({
                testName: 'Delayed Completion - Multiple Polls',
                passed: false,
                error: error.message
            });
            return false;
        }
    }

    /**
     * Configure a specific test scenario on the mock server
     */
    async configureScenario(recordId, scenario) {
        const response = await fetch(`http://localhost:3001/test/configure/${recordId}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ scenario })
        });
        
        if (!response.ok) {
            throw new Error(`Failed to configure scenario: ${response.statusText}`);
        }
        
        const result = await response.json();
        console.log(`⚙️ Configured scenario: ${result.message}`);
    }

    /**
     * Reset mock server state
     */
    async resetServer() {
        const response = await fetch('http://localhost:3001/test/reset', {
            method: 'POST'
        });
        
        if (response.ok) {
            console.log('🔄 Mock server state reset');
        }
    }

    /**
     * Run all real integration tests
     */
    async runAllTests() {
        console.log('🚀 Starting REAL Integration Tests with Mock Server');
        console.log('=' .repeat(80));
        console.log('🎯 Testing actual main.js against controlled API responses');
        console.log('📡 Using real HTTP calls to mock SearchLeads endpoints');
        console.log('');

        try {
            await this.setup();

            // Run tests
            await this.testImmediateCompletion();
            await this.testDelayedCompletion();

            // Generate report
            this.generateReport();

        } catch (error) {
            console.error('💥 Integration test setup failed:', error);
        } finally {
            await this.cleanup();
        }
    }

    /**
     * Generate test report
     */
    generateReport() {
        console.log('\n📊 REAL INTEGRATION TEST REPORT');
        console.log('=' .repeat(80));

        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(test => test.passed).length;

        console.log(`📈 Total Tests: ${totalTests}`);
        console.log(`✅ Passed: ${passedTests}`);
        console.log(`❌ Failed: ${totalTests - passedTests}`);
        console.log(`📊 Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
        console.log('');

        this.testResults.forEach((test, index) => {
            console.log(`${index + 1}. ${test.testName}: ${test.passed ? '✅ PASSED' : '❌ FAILED'}`);
            if (test.error) {
                console.log(`   Error: ${test.error}`);
            }
            if (test.details) {
                console.log(`   Details: ${JSON.stringify(test.details, null, 2)}`);
            }
        });

        console.log('');
        if (passedTests === totalTests) {
            console.log('🎉 ALL REAL INTEGRATION TESTS PASSED!');
            console.log('✅ The refactored main.js works correctly with real HTTP calls');
            console.log('✅ Immediate termination on Completed status verified');
            console.log('✅ Polling logic handles multiple attempts correctly');
        } else {
            console.log('⚠️ Some integration tests failed. Review the main.js implementation.');
        }

        console.log('=' .repeat(80));
    }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    const tester = new RealIntegrationTester();
    tester.runAllTests().catch(console.error);
}

export default RealIntegrationTester;
