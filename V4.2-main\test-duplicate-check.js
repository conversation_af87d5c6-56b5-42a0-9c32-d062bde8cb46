import MockApiServer from './test-mock-api-server.js';
import { run } from './test-main-standalone.js';

console.log('🔍 DUPLICATE REQUEST CHECK');
console.log('=' .repeat(60));
console.log('🎯 Verifying SEARCHLEADS_API_URL is called only ONCE per execution');
console.log('');

async function checkForDuplicates() {
    let mockServer = null;
    
    try {
        // Start mock server with request tracking
        console.log('🔧 Starting mock server with request tracking...');
        mockServer = new MockApiServer(3001);
        await mockServer.start();
        console.log('✅ Mock server started');
        
        // Wait for server to be ready
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Reset request tracking
        mockServer.resetRequestTracking();
        
        // Configure immediate completion to minimize status calls
        const recordId = 'duplicate-test-001';
        mockServer.enrichmentSessions.set(recordId, {
            currentStep: 0,
            statusSequence: [
                mockServer.generator.generateResponse('Completed') // Immediate completion
            ],
            startTime: Date.now(),
            scenario: 'immediate-completion'
        });

        console.log('🚀 Executing main logic...');
        console.log('📋 This should make exactly 1 start request and 1 status request');
        console.log('');
        
        const testInput = {
            apolloLink: "https://app.apollo.io/#/duplicate-check",
            noOfLeads: 1000,
            fileName: "duplicate-check"
        };

        try {
            const result = await run(testInput);
            console.log(`✅ Execution completed with status: ${result.enrichment_status}`);
        } catch (error) {
            console.log(`❌ Execution failed: ${error.message}`);
        }
        
        // Get request statistics
        const stats = mockServer.getRequestStats();
        
        console.log('\n📊 REQUEST STATISTICS');
        console.log('=' .repeat(60));
        console.log(`📤 START requests (SEARCHLEADS_API_URL): ${stats.startRequests}`);
        console.log(`📥 STATUS requests (SEARCHLEADS_STATUS_URL): ${stats.statusRequests}`);
        console.log(`📊 Total requests: ${stats.totalRequests}`);
        console.log('');
        
        // Detailed request log
        console.log('📋 DETAILED REQUEST LOG:');
        stats.requestLog.forEach((req, index) => {
            if (req.type === 'start') {
                console.log(`   ${index + 1}. START at ${req.timestamp}`);
                console.log(`      Apollo Link: ${req.body.apolloLink}`);
                console.log(`      File Name: ${req.body.fileName}`);
            } else {
                console.log(`   ${index + 1}. STATUS at ${req.timestamp}`);
                console.log(`      Record ID: ${req.recordId}`);
            }
        });
        
        // Analysis
        console.log('\n🔍 DUPLICATE ANALYSIS:');
        console.log('=' .repeat(60));
        
        if (stats.startRequests === 1) {
            console.log('✅ PERFECT: Exactly 1 start request detected');
            console.log('✅ NO DUPLICATION: SEARCHLEADS_API_URL called only once');
            console.log('✅ Implementation is working correctly');
        } else if (stats.startRequests > 1) {
            console.log('🚨 PROBLEM DETECTED: Multiple start requests!');
            console.log(`❌ SEARCHLEADS_API_URL called ${stats.startRequests} times`);
            console.log('🔧 This indicates duplicate API calls in the implementation');
            
            // Check for identical requests
            const startRequests = stats.requestLog.filter(req => req.type === 'start');
            if (startRequests.length > 1) {
                const firstRequest = startRequests[0];
                const duplicates = startRequests.slice(1).filter(req => 
                    JSON.stringify(req.body) === JSON.stringify(firstRequest.body)
                );
                
                if (duplicates.length > 0) {
                    console.log(`🚨 IDENTICAL DUPLICATES: ${duplicates.length + 1} identical requests found!`);
                } else {
                    console.log('📋 Different request bodies - not identical duplicates');
                }
            }
        } else {
            console.log('⚠️ UNEXPECTED: No start requests detected');
            console.log('🔧 This might indicate a test setup issue');
        }
        
        if (stats.statusRequests >= 1) {
            console.log(`✅ STATUS REQUESTS: ${stats.statusRequests} (expected for polling)`);
        } else {
            console.log('⚠️ No status requests detected - unexpected');
        }
        
        console.log('');
        console.log('🎯 FINAL VERDICT:');
        if (stats.startRequests === 1) {
            console.log('🎉 NO DUPLICATE REQUESTS - IMPLEMENTATION IS CORRECT');
        } else {
            console.log('❌ DUPLICATE REQUESTS DETECTED - NEEDS INVESTIGATION');
        }
        
        console.log('=' .repeat(60));
        
    } catch (error) {
        console.error('💥 Duplicate check failed:', error.message);
        console.error('Stack:', error.stack);
    } finally {
        if (mockServer) {
            console.log('\n🧹 Stopping mock server...');
            await mockServer.stop();
            console.log('✅ Cleanup complete');
        }
    }
}

checkForDuplicates();
