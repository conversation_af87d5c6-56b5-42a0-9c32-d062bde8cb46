import MockApiServer from './test-mock-api-server.js';
import { spawn } from 'child_process';
import fs from 'fs';

/**
 * Simple Integration Test
 * Tests the actual standalone main against a mock server
 */

console.log('🚀 Starting Simple Integration Test');
console.log('=' .repeat(60));

async function runSimpleIntegrationTest() {
    let mockServer = null;
    
    try {
        // 1. Start mock server
        console.log('🔧 Starting mock API server...');
        mockServer = new MockApiServer(3001);
        await mockServer.start();
        console.log('✅ Mock server started successfully');

        // 2. Backup and replace .env
        console.log('📋 Setting up test environment...');
        if (fs.existsSync('.env')) {
            fs.copyFileSync('.env', '.env.backup');
        }
        fs.copyFileSync('.env.test', '.env');
        console.log('✅ Test environment configured');

        // 3. Wait for server to be ready
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 4. Test the standalone main
        console.log('🧪 Running standalone main with test input...');
        
        const testInput = {
            apolloLink: "https://app.apollo.io/#/test",
            noOfLeads: 1000,
            fileName: "test-integration"
        };

        const result = await runStandaloneMain(testInput);
        
        console.log('📊 Test Results:');
        console.log(`   Exit Code: ${result.code}`);
        console.log(`   Success: ${result.success}`);
        console.log(`   Has Completion Message: ${result.stdout.includes('Enrichment completed successfully')}`);
        console.log(`   Has Immediate Stop: ${result.stdout.includes('Stopping polling immediately')}`);
        
        if (result.stdout) {
            console.log('\n📤 STDOUT Output:');
            console.log(result.stdout);
        }
        
        if (result.stderr) {
            console.log('\n📤 STDERR Output:');
            console.log(result.stderr);
        }

        const testPassed = result.success && 
                          result.stdout.includes('Enrichment completed successfully') &&
                          result.stdout.includes('Stopping polling immediately');

        console.log(`\n🎯 FINAL RESULT: ${testPassed ? '✅ PASSED' : '❌ FAILED'}`);
        
        if (testPassed) {
            console.log('🎉 Real integration test PASSED!');
            console.log('✅ The refactored implementation works with real HTTP calls');
            console.log('✅ Immediate termination on Completed status verified');
        } else {
            console.log('⚠️ Real integration test FAILED');
            console.log('❌ Check the implementation or test setup');
        }

    } catch (error) {
        console.error('💥 Integration test failed:', error.message);
    } finally {
        // Cleanup
        console.log('\n🧹 Cleaning up...');
        
        if (mockServer) {
            await mockServer.stop();
            console.log('✅ Mock server stopped');
        }
        
        if (fs.existsSync('.env.backup')) {
            fs.copyFileSync('.env.backup', '.env');
            fs.unlinkSync('.env.backup');
            console.log('✅ Original .env restored');
        }
        
        console.log('🏁 Integration test completed');
    }
}

/**
 * Run the standalone main with input
 */
function runStandaloneMain(testInput, timeout = 30000) {
    return new Promise((resolve, reject) => {
        console.log('🚀 Spawning test-main-standalone.js process...');
        
        const child = spawn('node', ['test-main-standalone.js'], {
            stdio: ['pipe', 'pipe', 'pipe'],
            env: { ...process.env }
        });

        let stdout = '';
        let stderr = '';
        let resolved = false;

        // Send input
        child.stdin.write(JSON.stringify(testInput));
        child.stdin.end();

        child.stdout.on('data', (data) => {
            const output = data.toString();
            stdout += output;
            console.log('📤', output.trim());
        });

        child.stderr.on('data', (data) => {
            const output = data.toString();
            stderr += output;
            console.log('📤 ERR:', output.trim());
        });

        child.on('close', (code) => {
            if (!resolved) {
                resolved = true;
                resolve({
                    code,
                    stdout,
                    stderr,
                    success: code === 0
                });
            }
        });

        child.on('error', (error) => {
            if (!resolved) {
                resolved = true;
                reject(error);
            }
        });

        // Timeout
        setTimeout(() => {
            if (!resolved) {
                resolved = true;
                child.kill();
                reject(new Error(`Test timed out after ${timeout}ms`));
            }
        }, timeout);
    });
}

// Run the test
runSimpleIntegrationTest().catch(console.error);
