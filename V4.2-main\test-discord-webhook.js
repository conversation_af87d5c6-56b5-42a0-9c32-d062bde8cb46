import axios from 'axios';

/**
 * Discord Webhook Integration for Failed and Cancelled Status Notifications
 */

export class DiscordWebhookNotifier {
    constructor() {
        this.webhookUrl = "https://discord.com/api/webhooks/1393445359191326720/yU9TVN53T9NthPKUKz_K0cgfnExIY13cVzBuVL8OffILCr2Zi-4kn7gyiqKWkgbLJ4I-";
    }

    /**
     * Send notification for failed enrichment request
     * @param {Object} requestData - The original request data
     * @param {Object} failureData - The failure response data
     */
    async sendFailedNotification(requestData, failureData) {
        const embed = {
            title: "🚨 Enrichment Request Failed",
            color: 0xFF0000, // Red color
            fields: [
                {
                    name: "Record ID",
                    value: failureData.record_id || "Unknown",
                    inline: true
                },
                {
                    name: "File Name",
                    value: failureData.file_name || requestData.fileName || "Unknown",
                    inline: true
                },
                {
                    name: "Requested Leads",
                    value: failureData.requested_leads_count || requestData.noOfLeads?.toString() || "Unknown",
                    inline: true
                },
                {
                    name: "Error Message",
                    value: failureData.error_message || "No error message provided",
                    inline: false
                },
                {
                    name: "Apollo Link",
                    value: failureData.apollo_link || requestData.apolloLink || "Not provided",
                    inline: false
                },
                {
                    name: "Failure Time",
                    value: failureData.failure_time || new Date().toISOString(),
                    inline: true
                },
                {
                    name: "Retry Possible",
                    value: failureData.retry_possible ? "Yes" : "No",
                    inline: true
                }
            ],
            timestamp: new Date().toISOString(),
            footer: {
                text: "SearchLeads Enrichment Service"
            }
        };

        return this.sendWebhook({
            content: "⚠️ **Enrichment Request Failed**",
            embeds: [embed]
        });
    }

    /**
     * Send notification for cancelled enrichment request
     * @param {Object} requestData - The original request data
     * @param {Object} cancellationData - The cancellation response data
     */
    async sendCancelledNotification(requestData, cancellationData) {
        const embed = {
            title: "⏹️ Enrichment Request Cancelled",
            color: 0xFFA500, // Orange color
            fields: [
                {
                    name: "Record ID",
                    value: cancellationData.record_id || "Unknown",
                    inline: true
                },
                {
                    name: "File Name",
                    value: cancellationData.file_name || requestData.fileName || "Unknown",
                    inline: true
                },
                {
                    name: "Requested Leads",
                    value: cancellationData.requested_leads_count || requestData.noOfLeads?.toString() || "Unknown",
                    inline: true
                },
                {
                    name: "Cancellation Reason",
                    value: cancellationData.cancellation_reason || "No reason provided",
                    inline: false
                },
                {
                    name: "Apollo Link",
                    value: cancellationData.apollo_link || requestData.apolloLink || "Not provided",
                    inline: false
                },
                {
                    name: "Cancelled Time",
                    value: cancellationData.cancelled_time || new Date().toISOString(),
                    inline: true
                },
                {
                    name: "Partial Results Available",
                    value: cancellationData.partial_results_available ? "Yes" : "No",
                    inline: true
                }
            ],
            timestamp: new Date().toISOString(),
            footer: {
                text: "SearchLeads Enrichment Service"
            }
        };

        return this.sendWebhook({
            content: "🛑 **Enrichment Request Cancelled**",
            embeds: [embed]
        });
    }

    /**
     * Send webhook message to Discord
     * @param {Object} payload - The Discord webhook payload
     */
    async sendWebhook(payload) {
        try {
            console.log('📤 Sending Discord notification...');
            const response = await axios.post(this.webhookUrl, payload, {
                headers: {
                    'Content-Type': 'application/json'
                },
                timeout: 10000 // 10 second timeout
            });
            
            console.log('✅ Discord notification sent successfully');
            return {
                success: true,
                status: response.status,
                statusText: response.statusText
            };
        } catch (error) {
            console.error('❌ Failed to send Discord notification:', error.message);
            
            // Log additional error details for debugging
            if (error.response) {
                console.error('Discord API Error Response:', {
                    status: error.response.status,
                    statusText: error.response.statusText,
                    data: error.response.data
                });
            }
            
            return {
                success: false,
                error: error.message,
                details: error.response?.data || null
            };
        }
    }

    /**
     * Test the webhook connection
     */
    async testConnection() {
        const testPayload = {
            content: "🧪 **Test Message from SearchLeads Enrichment Service**",
            embeds: [{
                title: "Connection Test",
                description: "This is a test message to verify Discord webhook connectivity.",
                color: 0x00FF00, // Green color
                timestamp: new Date().toISOString(),
                footer: {
                    text: "SearchLeads Test Suite"
                }
            }]
        };

        return this.sendWebhook(testPayload);
    }
}

export default DiscordWebhookNotifier;
