import { v4 as uuidv4 } from 'uuid';

/**
 * Test Webhook Response Generator
 * Automatically generates realistic webhook responses for testing all enrichment_status values
 */

export class TestWebhookGenerator {
    constructor() {
        this.baseResponse = {
            record_id: uuidv4(),
            apollo_link: "https://app.apollo.io/#/people?finderViewId=6674b20eecfedd000184539f&organizationNumEmployeesRanges[]=1%2C10&page=1",
            file_name: "test",
            spreadsheet_url: "https://docs.google.com/spreadsheets/d/1pdrYoexadNmJTlDBZBDYrg02jhbVNYgQSXNBM3WOM_4/edit?gid=965250855#gid=965250855",
            requested_leads_count: "1000"
        };
    }

    /**
     * Generate response for InProgress status
     */
    generateInProgressResponse() {
        return [{
            ...this.baseResponse,
            enrichment_status: "InProgress",
            progress_percentage: Math.floor(Math.random() * 80) + 10, // 10-89%
            estimated_completion_time: new Date(Date.now() + Math.random() * 3600000).toISOString()
        }];
    }

    /**
     * Generate response for Completed status
     */
    generateCompletedResponse() {
        return [{
            ...this.baseResponse,
            enrichment_status: "Completed",
            enriched_records: "987",
            credits_involved: "247",
            completion_time: new Date().toISOString(),
            success_rate: "98.7%"
        }];
    }

    /**
     * Generate response for inqueue status
     */
    generateInQueueResponse() {
        return [{
            ...this.baseResponse,
            enrichment_status: "inqueue",
            queue_position: Math.floor(Math.random() * 50) + 1,
            estimated_start_time: new Date(Date.now() + Math.random() * 1800000).toISOString()
        }];
    }

    /**
     * Generate response for Failed status
     */
    generateFailedResponse() {
        const errorReasons = [
            "Invalid Apollo link provided",
            "API rate limit exceeded",
            "Insufficient credits",
            "Network timeout",
            "Invalid search parameters"
        ];
        
        return [{
            ...this.baseResponse,
            enrichment_status: "Failed",
            error_message: errorReasons[Math.floor(Math.random() * errorReasons.length)],
            failure_time: new Date().toISOString(),
            retry_possible: Math.random() > 0.5
        }];
    }

    /**
     * Generate response for Cancelled status
     */
    generateCancelledResponse() {
        return [{
            ...this.baseResponse,
            enrichment_status: "Cancelled",
            cancellation_reason: "User requested cancellation",
            cancelled_time: new Date().toISOString(),
            partial_results_available: Math.random() > 0.7
        }];
    }

    /**
     * Generate response based on status type
     */
    generateResponse(statusType) {
        switch (statusType.toLowerCase()) {
            case 'inprogress':
                return this.generateInProgressResponse();
            case 'completed':
                return this.generateCompletedResponse();
            case 'inqueue':
                return this.generateInQueueResponse();
            case 'failed':
                return this.generateFailedResponse();
            case 'cancelled':
                return this.generateCancelledResponse();
            default:
                throw new Error(`Unknown status type: ${statusType}`);
        }
    }

    /**
     * Generate a sequence of responses for testing polling behavior
     * @param {string} finalStatus - The final status to end with
     * @param {number} intermediateSteps - Number of intermediate responses before final
     */
    generatePollingSequence(finalStatus, intermediateSteps = 3) {
        const sequence = [];
        
        // Add intermediate responses (InProgress or inqueue)
        for (let i = 0; i < intermediateSteps; i++) {
            const intermediateStatus = Math.random() > 0.5 ? 'InProgress' : 'inqueue';
            sequence.push(this.generateResponse(intermediateStatus));
        }
        
        // Add final response
        sequence.push(this.generateResponse(finalStatus));
        
        return sequence;
    }

    /**
     * Get all possible status types for testing
     */
    getAllStatusTypes() {
        return ['InProgress', 'Completed', 'inqueue', 'Failed', 'Cancelled'];
    }
}

export default TestWebhookGenerator;
